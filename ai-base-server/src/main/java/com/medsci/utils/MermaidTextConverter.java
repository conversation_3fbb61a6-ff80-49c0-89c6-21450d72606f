package com.medsci.utils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * Mermaid文本转换工具类
 * 用于将包含Mermaid图表的文本在数据库存储格式和可读格式之间转换
 */
public class MermaidTextConverter {

    /**
     * 将多行文本转换为数据库存储格式（单行字符串，使用\n作为换行符）
     * 
     * @param multiLineText 多行文本
     * @return 适合数据库存储的单行字符串
     */
    public static String convertToDbFormat(String multiLineText) {
        if (multiLineText == null) {
            return null;
        }
        
        return multiLineText
                .replace("\r\n", "\\n")  // Windows换行符
                .replace("\n", "\\n")    // Unix换行符
                .replace("\r", "\\n")    // Mac换行符
                .replace("\"", "\\\"")   // 转义双引号
                .replace("'", "\\'");    // 转义单引号
    }

    /**
     * 将数据库存储格式转换为可读的多行文本
     * 
     * @param dbText 数据库中存储的文本
     * @return 多行可读文本
     */
    public static String convertFromDbFormat(String dbText) {
        if (dbText == null) {
            return null;
        }
        
        return dbText
                .replace("\\n", "\n")    // 恢复换行符
                .replace("\\\"", "\"")   // 恢复双引号
                .replace("\\'", "'");    // 恢复单引号
    }

    /**
     * 从文件读取内容并转换为数据库格式
     * 
     * @param filePath 文件路径
     * @return 数据库格式的字符串
     * @throws IOException 文件读取异常
     */
    public static String readFileAndConvertToDbFormat(String filePath) throws IOException {
        String content = Files.readString(Paths.get(filePath));
        return convertToDbFormat(content);
    }

    /**
     * 将数据库格式的文本写入文件
     * 
     * @param dbText 数据库格式的文本
     * @param filePath 输出文件路径
     * @throws IOException 文件写入异常
     */
    public static void writeDbTextToFile(String dbText, String filePath) throws IOException {
        String readableText = convertFromDbFormat(dbText);
        Files.writeString(Paths.get(filePath), readableText);
    }

    /**
     * 验证Mermaid语法是否包含基本结构
     * 
     * @param mermaidText Mermaid文本
     * @return 是否包含有效的Mermaid语法
     */
    public static boolean isValidMermaidSyntax(String mermaidText) {
        if (mermaidText == null || mermaidText.trim().isEmpty()) {
            return false;
        }
        
        String text = mermaidText.toLowerCase();
        return text.contains("```mermaid") && 
               text.contains("```") && 
               (text.contains("graph") || text.contains("flowchart") || 
                text.contains("sequencediagram") || text.contains("classDiagram"));
    }

    /**
     * 获取文本的字节长度（用于检查是否超出数据库字段限制）
     * 
     * @param text 文本内容
     * @return 字节长度
     */
    public static int getByteLength(String text) {
        if (text == null) {
            return 0;
        }
        return text.getBytes().length;
    }

    /**
     * 检查文本是否超出指定的字节长度限制
     * 
     * @param text 文本内容
     * @param maxBytes 最大字节数
     * @return 是否超出限制
     */
    public static boolean exceedsMaxLength(String text, int maxBytes) {
        return getByteLength(text) > maxBytes;
    }

    /**
     * 示例用法
     */
    public static void main(String[] args) {
        try {
            // 读取文件并转换为数据库格式
            String dbFormat = readFileAndConvertToDbFormat("ai-base-server/src/main/resources/test_for_database.txt");
            
            System.out.println("=== 数据库存储格式 ===");
            System.out.println(dbFormat);
            System.out.println("\n字节长度: " + getByteLength(dbFormat));
            
            // 验证Mermaid语法
            System.out.println("Mermaid语法有效: " + isValidMermaidSyntax(dbFormat));
            
            // 转换回可读格式
            String readableFormat = convertFromDbFormat(dbFormat);
            System.out.println("\n=== 可读格式 ===");
            System.out.println(readableFormat.substring(0, Math.min(200, readableFormat.length())) + "...");
            
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
