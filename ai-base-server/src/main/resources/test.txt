（1）第一部分：临床与多组学相关性确认

```mermaid
graph TD
    subgraph part1 ["第一部分"]
        A[TCGA/GEO分析] --> A1[差异表达]
        A --> A2[预后分析]
        A --> A3[免疫浸润]
        B[自建队列验证] --> B1[80例CRC样本]
        B1 --> B2[IHC/IF检测]
        B2 --> B3[RT-qPCR验证]
    end

    %% Styling
    style part1 fill:#F0F8FF,stroke:#4A90E2
    classDef mainNode fill:#64B5F6,stroke:#0D47A1,color:white,stroke-width:2px
    classDef subNode fill:#BBDEFB,stroke:#1976D2,color:black,stroke-width:1.5px
    class A,B mainNode
    class A1,A2,A3,B1,B2,B3 subNode
```

（2）第二、三部分：m7G修饰机制研究

```mermaid
graph TD
    subgraph part2 ["第二、三部分"]
        C[m7G修饰鉴定] --> C1["全转录组LC-MS/MS<br/>+ Nanopore"]
        C --> C2["SOX2靶向<br/>m7G-site-seq + LC-MS/MS"]
        D[METTL1介导机制] --> D1["构建METTL1/WDR4<br/>敲除/过表达细胞"]
        D1 --> D2[脱帽-m7G-RIP-qPCR]
        D1 --> D3[eCLIP-seq + Motif分析]
        D1 --> D4["5′UTR-荧光素酶报告"]
    end

    %% Styling
    style part2 fill:#F0F8FF,stroke:#4A90E2
    classDef mainNode fill:#42A5F5,stroke:#0D47A1,color:white,stroke-width:2px
    classDef subNode fill:#90CAF9,stroke:#1976D2,color:black,stroke-width:1.5px
    class C,D mainNode
    class C1,C2,D1,D2,D3,D4 subNode
```

（3）第四、五部分：功能与下游调控验证

```mermaid
graph TD
    subgraph part3 ["第四、五部分"]
        E[功能学验证] --> E1["干细胞特性<br/>(球形成/流式/化疗耐受)"]
        E --> E2["拯救实验<br/>(METTL1-KD + SOX2-OE)"]
        E --> E3[T细胞共培养抑制实验]
        F[SOX2-PD-L1调控] --> F1["ChIP-qPCR → ChIP-seq"]
        F --> F2["PD-L1启动子<br/>WT vs Motif突变报告基因"]
    end

    %% Styling
    style part3 fill:#F0F8FF,stroke:#4A90E2
    classDef mainNode fill:#2196F3,stroke:#0D47A1,color:white,stroke-width:2px
    classDef subNode fill:#64B5F6,stroke:#1976D2,color:black,stroke-width:1.5px
    class E,F mainNode
    class E1,E2,E3,F1,F2 subNode
```

（4）第六部分：动物实验与联合治疗

```mermaid
graph TD
    subgraph part4 ["第六部分"]
        G[裸鼠皮下成瘤] --> G1["分组: 对照, sh-METTL1,<br/>SOX2-OE, 联合组"]
        H[Syngeneic MC38模型] --> H1["分组: Vehicle, sh-METTL1,<br/>αPD-1, 联合治疗"]
        I[终点检测] --> I1[肿瘤体积/生存]
        I --> I2["免疫组化<br/>(CD8+ T细胞, Granzyme B)"]
    end

    %% Styling
    style part4 fill:#F0F8FF,stroke:#4A90E2
    classDef mainNode fill:#1976D2,stroke:#0D47A1,color:white,stroke-width:2px
    classDef subNode fill:#42A5F5,stroke:#1976D2,color:white,stroke-width:1.5px
    class G,H,I mainNode
    class G1,H1,I1,I2 subNode
```\n\n